import 'package:audioplayers/audioplayers.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/pronunciation_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/pronunciation_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/audio_record.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/circular_progress_indicator_custom.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/gradient_circular_progress_indicator.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/sticky_header.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class PronunciationChallengeScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  const PronunciationChallengeScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<PronunciationChallengeScreen> createState() =>
      _PronunciationChallengeScreenState();
}

class _PronunciationChallengeScreenState
    extends ConsumerState<PronunciationChallengeScreen>
    with TickerProviderStateMixin {
  late AsyncValue<PronunciationState> viewState;
  late PronunciationController viewModel;

  final FirebaseStorage storage = FirebaseStorage.instance;
  bool _isAnimationComplete = false;
  late final AnimationController _animationController;
  late final Animation<double> _animation;
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    )..addStatusListener(_handleAnimationStatus);

    _animationController.forward();
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed && !_isAnimationComplete) {
      _animationController.reverse();
    } else if (status == AnimationStatus.dismissed) {
      if (mounted) {
        setState(() {
          _isAnimationComplete = true;
        });
      }
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final prov = pronunciationControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
    );

    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);

    ref.listen(prov, ((previous, next) {
      next.maybeWhen(
        error: (error, track) {
          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(error.toString())));
          }
        },
        orElse: () {},
      );
    }));

    return switch (viewState) {
      AsyncData()
          when viewState.value != null &&
              viewState.value!.isNewSubpart &&
              !_isAnimationComplete =>
        _transition(),
      AsyncData() when viewState.value != null => _body(),
      AsyncError() => _body(),
      AsyncLoading() => const AppLoading(),
      _ => const SizedBox.shrink(),
    };
  }

  Widget _transition() => FadeTransition(
    opacity: _animation,
    child: Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFF2F2),
              Color(0xffFDD8D8),
              Color(0xffFFECEC),
              Color(0xffFFFFFF),
            ],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
          ),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 46),
          child: ListView(
            children: [
              const SizedBox(height: 82),
              if (viewState.value?.currentPath != null)
                Text(
                  '${context.loc.part} ${viewState.value!.currentPath!.partOrder! + 1}',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              const SizedBox(height: 42),
              Container(
                height: 252,
                width: 252,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      Color(0xffFE754C),
                      Color(0xffE21F29),
                      Color(0xffC3151F),
                    ],
                    begin: Alignment.bottomLeft,
                    end: Alignment.topRight,
                  ),
                  image: DecorationImage(
                    image: AssetImage(
                      '$assetImageMainLesson/pronunciation_challenge/BG6-Android.png',
                    ),
                    fit: BoxFit.scaleDown,
                  ),
                ),
              ),
              const SizedBox(height: 32),
              Center(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xff421507), Color(0xffCA1E23)],
                      begin: Alignment.bottomLeft,
                      end: Alignment.topRight,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 5.5,
                    vertical: 2,
                  ),
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    viewState.value?.currentPath?.partTitle ?? '',
                    style: Theme.of(
                      context,
                    ).textTheme.titleLarge?.copyWith(color: Colors.white),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                viewState.value?.currentPath?.subpartTitle ?? '',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          ),
        ),
      ),
    ),
  );

  Widget _body() {
    if (viewState.value == null) return const SizedBox.shrink();

    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          ListView(
            children: [
              const SizedBox(height: 60),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 65),
                child: LinearProgressIndicator(
                  value: viewModel.index + 1 / viewModel.totalPaths,
                  backgroundColor: const Color(0xffFFDAD2),
                ),
              ),
              const SizedBox(height: 24),
              if (viewState.value!.data != null)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  width: MediaQuery.of(context).size.width - 32,
                  height: (MediaQuery.of(context).size.width - 32) * 1.3,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: Color(0xffFFEDEB),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.08),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                        child: CachedNetworkImage(
                          imageUrl: viewState.value!.data?.image ?? '',
                          fit: BoxFit.fitWidth,
                          placeholder:
                              (context, url) => Container(
                                color: Colors.grey[300],
                                child: const Center(child: LoadingCircle()),
                              ),
                          errorWidget:
                              (context, url, error) => Container(
                                color: Colors.grey[300], // Error background
                                child: const Center(child: Icon(Icons.error)),
                              ),
                        ),
                      ),
                    ],
                  ),
                ),
              const SizedBox(height: 18),
              InkWell(
                onTap: () async {
                  if (!viewState.value!.isRecording) {
                    _audioPlayer.play(UrlSource(viewState.value!.data!.audio));
                  }

                  // if (viewState.value?.data?.audio != null) {
                  //   await ref
                  //       .read(audioPlayerProvider)
                  //       .play(UrlSource(viewState.value!.data!.audio));
                  // }
                },
                child: Container(
                  margin: const EdgeInsets.all(16),
                  padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                  decoration: BoxDecoration(
                    color: Color(0xffFFEDEB),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.white),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(),
                      Text(
                        viewState.value!.data?.caption ?? '',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Color(0xff540005),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.g_translate,
                          size: 32,
                          color: Color(0xff540005),
                        ),
                        onPressed: () {},
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 18),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 18),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.06),
                      blurRadius: 10,
                      offset: const Offset(0, 3),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Text(
                  viewState.value!.data?.description ?? '',
                  style: const TextStyle(
                    color: Color.fromARGB(255, 127, 117, 116),
                  ),
                ),
              ),
              const SizedBox(height: 18),
              if (viewState.value!.isRecord)
                Recorder(
                  onStart: () async {
                    _audioPlayer.stop();
                    viewModel.changeRecordingState();
                    // await ref
                    //     .read(audioPlayerProvider)
                    //     .stop();
                  },
                  onStop: (path) {
                    if (mounted) {
                      viewModel.uploadAudio(path: path, context: context);
                      viewModel.changeRecordingState();
                    }
                  },
                ),
            ],
          ),
          StickyHeader(
            title: viewState.value?.currentPath?.subpartTitle ?? '',
            // subtitle: viewModel.prepareTitle(),
          ),
          if (viewState.value!.isLoading)
            const Positioned(
              bottom: 72,
              child: GradientCircularProgressIndicator(size: 70),
            ),
        ],
      ),
    );
  }
}

class SpeechBubblePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..shader = const LinearGradient(
            colors: [Color(0xFFF15A24), Color(0xFFFB6E47)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
          ..style = PaintingStyle.fill;

    final path =
        Path()
          ..moveTo(size.width * 0.05, size.height * 0.15)
          ..lineTo(size.width * 0.95, size.height * 0.15)
          ..quadraticBezierTo(
            size.width * 1.00,
            size.height * 0.15,
            size.width * 1.00,
            size.height * 0.25,
          )
          ..lineTo(size.width * 1.00, size.height * 0.75)
          ..quadraticBezierTo(
            size.width * 1.00,
            size.height * 0.85,
            size.width * 0.95,
            size.height * 0.85,
          )
          ..lineTo(size.width * 0.30, size.height * 0.85)
          ..lineTo(size.width * 0.20, size.height * 0.95)
          ..lineTo(size.width * 0.20, size.height * 0.85)
          ..lineTo(size.width * 0.05, size.height * 0.85)
          ..quadraticBezierTo(
            size.width * 0.00,
            size.height * 0.85,
            size.width * 0.00,
            size.height * 0.75,
          )
          ..lineTo(size.width * 0.00, size.height * 0.25)
          ..quadraticBezierTo(
            size.width * 0.00,
            size.height * 0.15,
            size.width * 0.05,
            size.height * 0.15,
          )
          ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
